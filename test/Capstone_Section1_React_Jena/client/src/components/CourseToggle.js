import React, { useState } from "react";

function CourseToggle() {
  const [isVisible, seIsVisible] = useState(false);
  const onSubmit = (e) => {
    e.preventDefault();
    seIsVisible(!isVisible);
  };
  return (
    <div>
      <button type="submit" onClick={onSubmit}>
        Show Description
      </button>
      {isVisible && (
        <p>
          This course covers React fundamentals including components, JSX, and
          props.
        </p>
      )}
    </div>
  );
}
export default CourseToggle;
