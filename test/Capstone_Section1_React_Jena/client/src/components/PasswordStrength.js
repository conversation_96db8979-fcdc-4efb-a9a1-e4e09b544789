import React, { useState } from "react";

// PasswordStrength Component
function PasswordStrength() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");
  const [isStrongPassword, setIsStrongPassword] = useState();

  const handleLogin = (e) => {
    e.preventDefault();
    if (password?.length < 6) {
      setMessage("Weak password");
      setIsStrongPassword(false);
    } else if (password?.length >= 6 && /\d/.test(password)) {
      setMessage("Strong password");
      setIsStrongPassword(true);
    }
  };

  return (
    <div style={{ padding: "20px" }}>
      <h2>Login to LMS</h2>
      <form onSubmit={handleLogin}>
        <input
          id="email"
          type="text"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          style={{ marginBottom: "10px", display: "block" }}
        />
        <input
          type="password"
          id="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          style={{ marginBottom: "10px", display: "block" }}
        />
        <button type="submit">Login</button>
      </form>
      {message && (
        <p style={{ color: isStrongPassword ? "green" : "red" }}>{message}</p>
      )}
    </div>
  );
}

export default PasswordStrength;
