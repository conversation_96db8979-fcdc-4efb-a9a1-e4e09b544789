const express = require('express');
const router = express.Router();
const School = require('../models/schoolModel');

router.post('/', async (req, res) => {
  const school = await School.create(req.body);
  res.send(school);
});

router.get('/', async (req, res) => {
  const schools = await School.find({});  
  res.send(schools);
});

router.post('/school', async (req, res) => {
  try {
    const { _id, name, address, principal } = req.body;
    const newSchool = new School({ _id, name, address, principal });
    await newSchool.save();
    res.status(201).json({ message: 'School inserted successfully' });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});
module.exports = router;
