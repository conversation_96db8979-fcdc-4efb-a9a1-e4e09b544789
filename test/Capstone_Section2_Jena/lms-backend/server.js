const express = require("express");

// Create an Express application
const app = express();

// Define the port number to run the server on
const PORT = 4000;

// 1. Middleware to parse JSON bodies in requests
app.use(express.json());
app.use(express.urlencoded({ extended: true })) 
// 2. Basic GET route for homepage
app.get("/", (req, res) => {
  res.send("Welcome to the LMS backend!");
});
// 3. GET route to return a sample list of courses
app.get("/courses", (req, res) => {
  const courses = [
    { id: 1, name: "React for Beginners" },
    { id: 2, name: "Intro to Data Science" },
    { id: 3, name: "AI Fundamentals" },
  ];
  res.json(courses); // Send the array as JSON
});

app.post("/enroll", (req, res) => {
  const { userId, courseId } = req.body;  
  if (!userId || !courseId) {
    res.status(400).json({ error: "Missing userId or courseId in request" });
  } else {
    res.json({ message: userId + " successfully enrolled in " + courseId });
  }
});

// 4. Start the Express server and listen on PORT
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
